#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

use super::guaiwu_redis_kongzhi::guaiwu_redis_kongzhi;
use super::guaiwu_rizhi_kongzhi::guaiwu_zifuchuan_changliangguanli;
use super::guaiwu_sql_kongzhi::guaiwu_sql_guanli;
use super::guaiwujiegouti::{guaiwu_fenlei_xinxi, guaiwu_liebiao_jieguo, guaiwu_liebiao_xiang};
use crate::chushihua::shujukuxitong::mysqlshujuku::mysql_lianjie::mysql_lianjie_guanli;
use crate::chushihua::shujukuxitong::redishujuku::redis_lianjie::redis_lianjie_guanli;
use crate::chushihua::shujukuxitong::youxishujuchuli::wupinshuju::wupinjiegouti::fenye_canshu;
use serde::{Deserialize, Serialize};
use sqlx::Row;

/// 名字查询类型枚举
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum mingzi_chaxun_leixing {
    /// 类名精确查询
    leiming_jingque,
    /// 名字精确查询
    mingzi_jingque,
    /// 名字模糊查询
    mingzi_mohu,
}

impl mingzi_chaxun_leixing {
    /// 转换为字符串标识
    pub fn to_string(&self) -> String {
        match self {
            Self::leiming_jingque => "leiming_jingque".to_string(),
            Self::mingzi_jingque => "mingzi_jingque".to_string(),
            Self::mingzi_mohu => "mingzi_mohu".to_string(),
        }
    }
}

/// 名字查询条件结构体
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct mingzi_chaxun_tiaojian {
    /// 查询类型
    pub chaxun_leixing: mingzi_chaxun_leixing,
    /// 查询值
    pub chaxun_zhi: String,
    /// 分页参数
    pub fenye_canshu: fenye_canshu,
}

impl mingzi_chaxun_tiaojian {
    /// 创建类名精确查询条件
    pub fn leiming_jingque(leiming: String, fenye_canshu: fenye_canshu) -> Self {
        Self {
            chaxun_leixing: mingzi_chaxun_leixing::leiming_jingque,
            chaxun_zhi: leiming,
            fenye_canshu,
        }
    }

    /// 创建名字精确查询条件
    pub fn mingzi_jingque(mingzi: String, fenye_canshu: fenye_canshu) -> Self {
        Self {
            chaxun_leixing: mingzi_chaxun_leixing::mingzi_jingque,
            chaxun_zhi: mingzi,
            fenye_canshu,
        }
    }

    /// 创建名字模糊查询条件
    pub fn mingzi_mohu(mingzi: String, fenye_canshu: fenye_canshu) -> Self {
        Self {
            chaxun_leixing: mingzi_chaxun_leixing::mingzi_mohu,
            chaxun_zhi: mingzi,
            fenye_canshu,
        }
    }
}

/// 怪物名字查询管理器
pub struct guaiwu_mingzi_chaxun_guanli {
    mysql_lianjie: mysql_lianjie_guanli,
    redis_lianjie: Option<redis_lianjie_guanli>,
}

impl guaiwu_mingzi_chaxun_guanli {
    /// 创建新的怪物名字查询管理实例
    pub fn new(mysql_lianjie: mysql_lianjie_guanli) -> Self {
        Self {
            mysql_lianjie,
            redis_lianjie: None,
        }
    }

    /// 创建带Redis连接的怪物名字查询管理实例
    pub fn new_with_redis(mysql_lianjie: mysql_lianjie_guanli, redis_lianjie: redis_lianjie_guanli) -> Self {
        Self {
            mysql_lianjie,
            redis_lianjie: Some(redis_lianjie),
        }
    }

    /// 统一名字查询入口
    pub async fn tongyong_mingzi_chaxun(&self, chaxun_tiaojian: &mingzi_chaxun_tiaojian) -> anyhow::Result<guaiwu_liebiao_jieguo> {
        // 尝试从Redis获取缓存
        if let Some(redis) = &self.redis_lianjie {
            let huancun_jian = guaiwu_zifuchuan_changliangguanli::shengcheng_redis_jian_mingzi_chaxun(
                &chaxun_tiaojian.chaxun_leixing.to_string(),
                &chaxun_tiaojian.chaxun_zhi,
                chaxun_tiaojian.fenye_canshu.yema,
                chaxun_tiaojian.fenye_canshu.meiye_shuliang
            );

            if let Ok(Some(huancun_shuju)) = redis.huoqu(&huancun_jian).await {
                if let Ok(mut liebiao_jieguo) = serde_json::from_str::<guaiwu_liebiao_jieguo>(&huancun_shuju) {
                    // 标记数据来源为Redis缓存
                    liebiao_jieguo.shuju_laiyuan = Some("redis".to_string());
                    return Ok(liebiao_jieguo);
                }
            }
        }

        // 从数据库查询
        let liebiao_jieguo = match chaxun_tiaojian.chaxun_leixing {
            mingzi_chaxun_leixing::leiming_jingque => {
                self.chaxun_by_leiming_jingque(&chaxun_tiaojian.chaxun_zhi, &chaxun_tiaojian.fenye_canshu).await
            }
            mingzi_chaxun_leixing::mingzi_jingque => {
                self.chaxun_by_mingzi_jingque(&chaxun_tiaojian.chaxun_zhi, &chaxun_tiaojian.fenye_canshu).await
            }
            mingzi_chaxun_leixing::mingzi_mohu => {
                self.chaxun_by_mingzi_mohu(&chaxun_tiaojian.chaxun_zhi, &chaxun_tiaojian.fenye_canshu).await
            }
        };

        match liebiao_jieguo {
            Ok(jieguo) => {
                // 缓存到Redis
                if let Some(redis) = &self.redis_lianjie {
                    let huancun_jian = guaiwu_zifuchuan_changliangguanli::shengcheng_redis_jian_mingzi_chaxun(
                        &chaxun_tiaojian.chaxun_leixing.to_string(),
                        &chaxun_tiaojian.chaxun_zhi,
                        chaxun_tiaojian.fenye_canshu.yema,
                        chaxun_tiaojian.fenye_canshu.meiye_shuliang
                    );

                    if let Ok(json_shuju) = serde_json::to_string(&jieguo) {
                        let _ = redis.shezhi(&huancun_jian, &json_shuju).await;
                        let _ = redis.shezhi_guoqi(&huancun_jian, guaiwu_zifuchuan_changliangguanli::mingzi_chaxun_huancun_shijian as i64).await;
                    }
                }

                Ok(jieguo)
            }
            Err(e) => {
                let cuowu_xinxi = match chaxun_tiaojian.chaxun_leixing {
                    mingzi_chaxun_leixing::leiming_jingque => {
                        guaiwu_zifuchuan_changliangguanli::shengcheng_cuowu_chaxun_by_leiming_shibai(&e.to_string())
                    }
                    mingzi_chaxun_leixing::mingzi_jingque => {
                        guaiwu_zifuchuan_changliangguanli::shengcheng_cuowu_chaxun_by_mingzi_jingque_shibai(&e.to_string())
                    }
                    mingzi_chaxun_leixing::mingzi_mohu => {
                        guaiwu_zifuchuan_changliangguanli::shengcheng_cuowu_chaxun_by_mingzi_mohu_shibai(&e.to_string())
                    }
                };
                crate::rizhixitong::rizhixitong_cuowu_with_moshi(
                    &cuowu_xinxi,
                    crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi
                );
                Ok(guaiwu_liebiao_jieguo::shibai(cuowu_xinxi))
            }
        }
    }

    /// 根据类名精确查询怪物列表
    pub async fn chaxun_by_leiming_jingque(&self, leiming: &str, fenye_canshu: &fenye_canshu) -> anyhow::Result<guaiwu_liebiao_jieguo> {
        // 先查询总数
        let zong_shuliang = self.chaxun_by_leiming_jingque_zongshu(leiming).await?;

        // 查询分页数据
        let sql = guaiwu_sql_guanli::sql_chaxun_by_leiming_jingque;
        let pianyi = fenye_canshu.jisuan_pianyi();

        match sqlx::query(sql)
            .bind(leiming)
            .bind(fenye_canshu.meiye_shuliang as i64)
            .bind(pianyi as i64)
            .fetch_all(self.mysql_lianjie.huoqu_lianjiechi()?)
            .await
        {
            Ok(rows) => {
                let guaiwu_liebiao = self.jiexi_chaxun_jieguo(rows)?;
                Ok(guaiwu_liebiao_jieguo::chenggong(guaiwu_liebiao, fenye_canshu, zong_shuliang))
            }
            Err(e) => {
                let cuowu_xinxi = guaiwu_zifuchuan_changliangguanli::shengcheng_cuowu_chaxun_by_leiming_shibai(&e.to_string());
                Err(anyhow::anyhow!(cuowu_xinxi))
            }
        }
    }

    /// 根据名字精确查询怪物列表
    pub async fn chaxun_by_mingzi_jingque(&self, mingzi: &str, fenye_canshu: &fenye_canshu) -> anyhow::Result<guaiwu_liebiao_jieguo> {
        // 先查询总数
        let zong_shuliang = self.chaxun_by_mingzi_jingque_zongshu(mingzi).await?;

        // 查询分页数据
        let sql = guaiwu_sql_guanli::sql_chaxun_by_mingzi_jingque;
        let pianyi = fenye_canshu.jisuan_pianyi();

        match sqlx::query(sql)
            .bind(mingzi)
            .bind(mingzi)
            .bind(fenye_canshu.meiye_shuliang as i64)
            .bind(pianyi as i64)
            .fetch_all(self.mysql_lianjie.huoqu_lianjiechi()?)
            .await
        {
            Ok(rows) => {
                let guaiwu_liebiao = self.jiexi_chaxun_jieguo(rows)?;
                Ok(guaiwu_liebiao_jieguo::chenggong(guaiwu_liebiao, fenye_canshu, zong_shuliang))
            }
            Err(e) => {
                let cuowu_xinxi = guaiwu_zifuchuan_changliangguanli::shengcheng_cuowu_chaxun_by_mingzi_jingque_shibai(&e.to_string());
                Err(anyhow::anyhow!(cuowu_xinxi))
            }
        }
    }

    /// 根据名字模糊查询怪物列表
    pub async fn chaxun_by_mingzi_mohu(&self, mingzi: &str, fenye_canshu: &fenye_canshu) -> anyhow::Result<guaiwu_liebiao_jieguo> {
        // 构造模糊查询模式
        let mohu_moshi = format!("%{}%", mingzi);

        // 先查询总数
        let zong_shuliang = self.chaxun_by_mingzi_mohu_zongshu(&mohu_moshi).await?;

        // 查询分页数据
        let sql = guaiwu_sql_guanli::sql_chaxun_by_mingzi_mohu;
        let pianyi = fenye_canshu.jisuan_pianyi();

        match sqlx::query(sql)
            .bind(&mohu_moshi)
            .bind(&mohu_moshi)
            .bind(fenye_canshu.meiye_shuliang as i64)
            .bind(pianyi as i64)
            .fetch_all(self.mysql_lianjie.huoqu_lianjiechi()?)
            .await
        {
            Ok(rows) => {
                let guaiwu_liebiao = self.jiexi_chaxun_jieguo(rows)?;
                Ok(guaiwu_liebiao_jieguo::chenggong(guaiwu_liebiao, fenye_canshu, zong_shuliang))
            }
            Err(e) => {
                let cuowu_xinxi = guaiwu_zifuchuan_changliangguanli::shengcheng_cuowu_chaxun_by_mingzi_mohu_shibai(&e.to_string());
                Err(anyhow::anyhow!(cuowu_xinxi))
            }
        }
    }

    /// 查询类名精确匹配的怪物总数
    async fn chaxun_by_leiming_jingque_zongshu(&self, leiming: &str) -> anyhow::Result<u64> {
        let sql = guaiwu_sql_guanli::sql_chaxun_by_leiming_jingque_zongshu;

        match sqlx::query(sql)
            .bind(leiming)
            .fetch_one(self.mysql_lianjie.huoqu_lianjiechi()?)
            .await
        {
            Ok(row) => {
                match row.try_get::<i64, _>("zong_shuliang") {
                    Ok(count) => Ok(count as u64),
                    Err(e) => {
                        let cuowu_xinxi = guaiwu_zifuchuan_changliangguanli::shengcheng_cuowu_chaxun_by_leiming_shibai(&e.to_string());
                        crate::rizhixitong::rizhixitong_cuowu_with_moshi(
                            &cuowu_xinxi,
                            crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi
                        );
                        Err(anyhow::anyhow!(cuowu_xinxi))
                    }
                }
            }
            Err(e) => {
                let cuowu_xinxi = guaiwu_zifuchuan_changliangguanli::shengcheng_cuowu_chaxun_by_leiming_shibai(&e.to_string());
                crate::rizhixitong::rizhixitong_cuowu_with_moshi(
                    &cuowu_xinxi,
                    crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi
                );
                Err(anyhow::anyhow!(cuowu_xinxi))
            }
        }
    }

    /// 查询名字精确匹配的怪物总数
    async fn chaxun_by_mingzi_jingque_zongshu(&self, mingzi: &str) -> anyhow::Result<u64> {
        let sql = guaiwu_sql_guanli::sql_chaxun_by_mingzi_jingque_zongshu;

        match sqlx::query(sql)
            .bind(mingzi)
            .bind(mingzi)
            .fetch_one(self.mysql_lianjie.huoqu_lianjiechi()?)
            .await
        {
            Ok(row) => {
                match row.try_get::<i64, _>("zong_shuliang") {
                    Ok(count) => Ok(count as u64),
                    Err(e) => {
                        let cuowu_xinxi = guaiwu_zifuchuan_changliangguanli::shengcheng_cuowu_chaxun_by_mingzi_jingque_shibai(&e.to_string());
                        crate::rizhixitong::rizhixitong_cuowu_with_moshi(
                            &cuowu_xinxi,
                            crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi
                        );
                        Err(anyhow::anyhow!(cuowu_xinxi))
                    }
                }
            }
            Err(e) => {
                let cuowu_xinxi = guaiwu_zifuchuan_changliangguanli::shengcheng_cuowu_chaxun_by_mingzi_jingque_shibai(&e.to_string());
                crate::rizhixitong::rizhixitong_cuowu_with_moshi(
                    &cuowu_xinxi,
                    crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi
                );
                Err(anyhow::anyhow!(cuowu_xinxi))
            }
        }
    }

    /// 查询名字模糊匹配的怪物总数
    async fn chaxun_by_mingzi_mohu_zongshu(&self, mohu_moshi: &str) -> anyhow::Result<u64> {
        let sql = guaiwu_sql_guanli::sql_chaxun_by_mingzi_mohu_zongshu;

        match sqlx::query(sql)
            .bind(mohu_moshi)
            .bind(mohu_moshi)
            .fetch_one(self.mysql_lianjie.huoqu_lianjiechi()?)
            .await
        {
            Ok(row) => {
                match row.try_get::<i64, _>("zong_shuliang") {
                    Ok(count) => Ok(count as u64),
                    Err(e) => {
                        let cuowu_xinxi = guaiwu_zifuchuan_changliangguanli::shengcheng_cuowu_chaxun_by_mingzi_mohu_shibai(&e.to_string());
                        crate::rizhixitong::rizhixitong_cuowu_with_moshi(
                            &cuowu_xinxi,
                            crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi
                        );
                        Err(anyhow::anyhow!(cuowu_xinxi))
                    }
                }
            }
            Err(e) => {
                let cuowu_xinxi = guaiwu_zifuchuan_changliangguanli::shengcheng_cuowu_chaxun_by_mingzi_mohu_shibai(&e.to_string());
                crate::rizhixitong::rizhixitong_cuowu_with_moshi(
                    &cuowu_xinxi,
                    crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi
                );
                Err(anyhow::anyhow!(cuowu_xinxi))
            }
        }
    }

    /// 解析查询结果行数据
    fn jiexi_chaxun_jieguo(&self, rows: Vec<sqlx::mysql::MySqlRow>) -> anyhow::Result<Vec<guaiwu_liebiao_xiang>> {
        let mut guaiwu_liebiao = Vec::new();

        for row in rows {
            if let (Ok(guaiwu_id), Ok(guaiwu_mingcheng)) = (
                row.try_get::<i32, _>("guaiwu_id"),
                row.try_get::<String, _>("guaiwu_mingcheng")
            ) {
                let guaiwu_leiming = row.try_get::<String, _>("guaiwu_leiming").ok();
                let guaiwufenlei = guaiwu_fenlei_xinxi::cong_shujuku_hang_jiexi(&row);

                guaiwu_liebiao.push(guaiwu_liebiao_xiang {
                    guaiwu_id,
                    guaiwu_mingcheng,
                    guaiwu_leiming,
                    guaiwufenlei,
                });
            }
        }

        Ok(guaiwu_liebiao)
    }

    /// 获取名字查询缓存统计信息
    pub async fn huoqu_mingzi_chaxun_huancun_tongji(&self) -> anyhow::Result<String> {
        if let Some(redis) = &self.redis_lianjie {
            let redis_kongzhi = guaiwu_redis_kongzhi::new(redis.clone());
            redis_kongzhi.huoqu_mingzi_chaxun_huancun_tongji().await
        } else {
            Ok(guaiwu_zifuchuan_changliangguanli::tongji_wei_qiyong_redis_huancun.to_string())
        }
    }

    /// 清除名字查询缓存
    pub async fn qingchu_mingzi_chaxun_huancun(&self) -> anyhow::Result<()> {
        if let Some(redis) = &self.redis_lianjie {
            let redis_kongzhi = guaiwu_redis_kongzhi::new(redis.clone());
            redis_kongzhi.qingchu_mingzi_chaxun_huancun().await
        } else {
            Ok(())
        }
    }
}
